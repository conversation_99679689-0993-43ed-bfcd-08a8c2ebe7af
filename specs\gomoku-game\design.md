# 在线五子棋游戏技术方案设计

## 系统架构概览

本项目采用前后端分离架构，基于腾讯云开发 CloudBase 平台构建全栈应用。

```mermaid
graph TB
    A[用户浏览器] --> B[Vue3 前端应用]
    B --> C[CloudBase JS SDK]
    C --> D[腾讯云开发平台]
    
    D --> E[云函数]
    D --> F[实时数据库]
    D --> G[云数据库]
    D --> H[静态网站托管]
    
    E --> E1[房间管理器]
    E --> E2[游戏处理器]
    E --> E3[WebSocket服务器]
    
    F --> F1[实时游戏状态]
    F --> F2[玩家连接状态]
    
    G --> G1[房间信息]
    G --> G2[游戏记录]
```

## 前端技术架构

### 技术栈选择

- **框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **路由**: Vue Router 4 (Hash Router)
- **状态管理**: Pinia
- **样式框架**: Tailwind CSS + DaisyUI
- **UI组件**: 自定义卡通风格组件
- **动画**: CSS3 Transitions + Vue Transition
- **实时通信**: CloudBase 实时数据库

### 前端架构设计

```mermaid
graph LR
    A[App.vue] --> B[Router]
    B --> C[HomePage]
    B --> D[GameRoom]
    B --> E[JoinRoom]
    
    C --> F[CreateRoomModal]
    C --> G[JoinRoomForm]
    
    D --> H[GameBoard]
    D --> I[PlayerInfo]
    D --> J[GameStatus]
    D --> K[ChatPanel]
    
    L[Stores] --> M[gameStore]
    L --> N[roomStore]
    L --> O[userStore]
    
    P[Utils] --> Q[cloudbase.js]
    P --> R[gameLogic.js]
    P --> S[constants.js]
```

### 页面结构设计

1. **首页 (HomePage)**
   - 创建房间按钮
   - 加入房间表单
   - 游戏说明

2. **游戏房间 (GameRoom)**
   - 游戏棋盘组件
   - 玩家信息面板
   - 游戏状态显示
   - 房间信息（房间号、分享按钮）

3. **加入房间页 (JoinRoom)**
   - 房间号输入
   - 密码输入（如需要）
   - 加入按钮

### 组件设计

```mermaid
graph TD
    A[GameBoard] --> B[ChessBoard]
    A --> C[ChessPiece]
    
    D[PlayerInfo] --> E[PlayerAvatar]
    D --> F[PlayerStatus]
    
    G[GameStatus] --> H[TurnIndicator]
    G --> I[Timer]
    G --> J[GameResult]
    
    K[RoomInfo] --> L[RoomNumber]
    K --> M[ShareButton]
    K --> N[PlayerList]
```

## 后端技术架构

### 云开发服务选择

1. **云函数**: 处理游戏逻辑和房间管理
2. **实时数据库**: 实现实时游戏状态同步
3. **云数据库**: 存储房间信息和游戏数据
4. **静态网站托管**: 部署前端应用

### 云函数设计

#### 1. 房间管理器 (room-manager)
```javascript
// 主要功能
- createRoom(password?) // 创建房间
- joinRoom(roomId, password?) // 加入房间
- leaveRoom(roomId, userId) // 离开房间
- getRoomInfo(roomId) // 获取房间信息
- cleanupExpiredRooms() // 清理过期房间
```

#### 2. 游戏处理器 (game-handler)
```javascript
// 主要功能
- startGame(roomId) // 开始游戏
- makeMove(roomId, userId, x, y) // 下棋
- checkWinner(board) // 检查胜负
- resetGame(roomId) // 重新开始
- validateMove(board, x, y) // 验证落子
```

#### 3. WebSocket服务器 (websocket-server)
```javascript
// 主要功能
- handleConnection() // 处理连接
- handleDisconnection() // 处理断开
- broadcastToRoom() // 房间广播
- sendToUser() // 发送给特定用户
```

### 数据库设计

#### 云数据库集合

1. **rooms** - 房间信息
```json
{
  "_id": "room_id",
  "roomNumber": "6位房间号",
  "password": "加密密码",
  "createdAt": "创建时间",
  "createdBy": "创建者ID",
  "status": "waiting|playing|finished",
  "maxPlayers": 2,
  "maxObservers": 10
}
```

2. **room_users** - 房间用户关系
```json
{
  "_id": "关系ID",
  "roomId": "房间ID",
  "userId": "用户ID",
  "role": "player|observer",
  "joinedAt": "加入时间",
  "isOnline": true
}
```

#### 实时数据库集合

1. **game_states** - 游戏状态
```json
{
  "roomId": {
    "board": "15x15棋盘数组",
    "currentPlayer": "black|white",
    "gameStatus": "waiting|playing|finished",
    "winner": "black|white|draw|null",
    "lastMove": {"x": 7, "y": 7, "player": "black"},
    "players": {
      "black": {"userId": "xxx", "name": "玩家1"},
      "white": {"userId": "yyy", "name": "玩家2"}
    },
    "observers": ["userId1", "userId2"],
    "updatedAt": "时间戳"
  }
}
```

2. **room_connections** - 连接状态
```json
{
  "roomId": {
    "connections": {
      "userId1": {"lastSeen": "时间戳", "isOnline": true},
      "userId2": {"lastSeen": "时间戳", "isOnline": false}
    }
  }
}
```

## 实时通信方案

### 使用云开发实时数据库

1. **监听游戏状态变化**
```javascript
// 前端监听游戏状态
db.collection('game_states').doc(roomId).watch({
  onChange: (snapshot) => {
    // 更新本地游戏状态
    updateGameState(snapshot.data)
  }
})
```

2. **监听房间连接状态**
```javascript
// 监听玩家在线状态
db.collection('room_connections').doc(roomId).watch({
  onChange: (snapshot) => {
    // 更新玩家在线状态
    updatePlayerStatus(snapshot.data.connections)
  }
})
```

## 游戏逻辑设计

### 五子棋规则实现

1. **棋盘表示**: 15x15二维数组，0=空，1=黑棋，2=白棋
2. **胜负判断**: 检查横、竖、斜四个方向是否有连续5子
3. **落子验证**: 检查位置是否为空、是否轮到该玩家

### 状态管理

使用 Pinia 管理全局状态：

```javascript
// gameStore.js
export const useGameStore = defineStore('game', {
  state: () => ({
    roomId: null,
    gameState: null,
    currentUser: null,
    isMyTurn: false
  }),
  actions: {
    async makeMove(x, y) {
      // 调用云函数下棋
    },
    async joinRoom(roomId, password) {
      // 加入房间逻辑
    }
  }
})
```

## 安全性设计

1. **输入验证**: 所有用户输入在云函数中验证
2. **权限控制**: 只有轮到的玩家才能下棋
3. **防作弊**: 游戏状态在服务端验证和更新
4. **密码加密**: 房间密码使用bcrypt加密存储
5. **频率限制**: 防止恶意创建房间和快速操作

## 性能优化

1. **前端优化**
   - 组件懒加载
   - 图片资源压缩
   - CSS/JS代码分割

2. **后端优化**
   - 云函数冷启动优化
   - 数据库索引优化
   - 实时数据库连接池管理

3. **网络优化**
   - CDN加速静态资源
   - 实时数据库连接复用
   - 数据传输压缩

## 部署方案

1. **前端部署**: 使用云开发静态网站托管
2. **云函数部署**: 使用CloudBase CLI自动部署
3. **数据库初始化**: 创建必要的集合和索引
4. **环境配置**: 开发环境和生产环境分离

## 测试策略

1. **单元测试**: 游戏逻辑函数测试
2. **集成测试**: 云函数接口测试
3. **端到端测试**: 完整游戏流程测试
4. **性能测试**: 并发用户压力测试
