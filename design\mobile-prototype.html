<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>五子棋游戏 - 移动端原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'ios-blue': '#007AFF',
                        'ios-green': '#34C759',
                        'ios-orange': '#FF9500',
                        'ios-red': '#FF3B30',
                        'ios-bg': '#F2F2F7',
                        'ios-card': '#FFFFFF',
                        'ios-gray': '#8E8E93',
                        'board-bg': '#DEB887',
                        'board-line': '#8B4513'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-primary { background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%); }
        .piece-black { background: radial-gradient(circle at 30% 30%, #4A4A4A, #1C1C1E); }
        .piece-white { background: radial-gradient(circle at 30% 30%, #FFFFFF, #F2F2F7); }
        .board-grid {
            background-image: 
                linear-gradient(to right, #8B4513 1px, transparent 1px),
                linear-gradient(to bottom, #8B4513 1px, transparent 1px);
            background-size: 20px 20px;
        }
        .btn-cartoon:active { transform: scale(0.95); }
        @media (max-width: 640px) {
            .board-grid { background-size: 18px 18px; }
        }
    </style>
</head>
<body class="bg-ios-bg min-h-screen">
    <!-- 移动端游戏房间 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部状态栏 -->
        <div class="bg-ios-card shadow-sm p-3">
            <div class="flex justify-between items-center text-sm">
                <div class="flex items-center space-x-2">
                    <span class="text-ios-gray">房间:</span>
                    <span class="font-mono font-bold">123456</span>
                </div>
                <div class="flex space-x-1">
                    <button class="bg-ios-blue text-white px-3 py-1 rounded-lg text-xs">分享</button>
                    <button class="bg-ios-red text-white px-3 py-1 rounded-lg text-xs">退出</button>
                </div>
            </div>
        </div>

        <!-- 玩家信息栏 -->
        <div class="bg-ios-card border-b border-ios-bg p-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 piece-black rounded-full border-2 border-ios-blue"></div>
                    <div>
                        <div class="font-semibold text-sm">玩家1</div>
                        <div class="text-xs text-ios-green">在线</div>
                    </div>
                </div>
                
                <div class="text-center">
                    <div class="text-lg font-bold">VS</div>
                    <div class="text-xs text-ios-gray">第7步</div>
                </div>
                
                <div class="flex items-center space-x-2">
                    <div>
                        <div class="font-semibold text-sm text-right">玩家2</div>
                        <div class="text-xs text-ios-green text-right">在线</div>
                    </div>
                    <div class="w-8 h-8 piece-white rounded-full border-2 border-gray-300"></div>
                </div>
            </div>
        </div>

        <!-- 游戏状态 -->
        <div class="bg-ios-orange text-white p-2 text-center">
            <div class="flex justify-center items-center space-x-4 text-sm">
                <div class="flex items-center space-x-1">
                    <div class="w-3 h-3 piece-black rounded-full animate-pulse"></div>
                    <span>黑棋回合</span>
                </div>
                <div class="bg-white bg-opacity-20 px-2 py-1 rounded">
                    <span class="font-mono">00:25</span>
                </div>
            </div>
        </div>

        <!-- 棋盘区域 -->
        <div class="flex-1 p-4 flex items-center justify-center">
            <div class="bg-board-bg rounded-lg p-3 board-grid aspect-square w-full max-w-sm relative" id="mobileBoard">
                <!-- 棋盘标记点 -->
                <div class="absolute w-1.5 h-1.5 bg-board-line rounded-full" style="top: 54px; left: 54px;"></div>
                <div class="absolute w-1.5 h-1.5 bg-board-line rounded-full" style="top: 54px; left: 144px;"></div>
                <div class="absolute w-1.5 h-1.5 bg-board-line rounded-full" style="top: 54px; left: 234px;"></div>
                <div class="absolute w-1.5 h-1.5 bg-board-line rounded-full" style="top: 144px; left: 54px;"></div>
                <div class="absolute w-1.5 h-1.5 bg-board-line rounded-full" style="top: 144px; left: 144px;"></div>
                <div class="absolute w-1.5 h-1.5 bg-board-line rounded-full" style="top: 144px; left: 234px;"></div>
                <div class="absolute w-1.5 h-1.5 bg-board-line rounded-full" style="top: 234px; left: 54px;"></div>
                <div class="absolute w-1.5 h-1.5 bg-board-line rounded-full" style="top: 234px; left: 144px;"></div>
                <div class="absolute w-1.5 h-1.5 bg-board-line rounded-full" style="top: 234px; left: 234px;"></div>
                
                <!-- 示例棋子 -->
                <div class="absolute w-5 h-5 piece-black rounded-full" style="top: 135px; left: 135px;"></div>
                <div class="absolute w-5 h-5 piece-white rounded-full" style="top: 153px; left: 153px;"></div>
                <div class="absolute w-5 h-5 piece-black rounded-full" style="top: 117px; left: 117px;"></div>
                <div class="absolute w-5 h-5 piece-white rounded-full" style="top: 171px; left: 171px;"></div>
                <div class="absolute w-5 h-5 piece-black rounded-full" style="top: 99px; left: 99px;"></div>
                
                <!-- 最后一步标记 -->
                <div class="absolute w-2 h-2 bg-ios-red rounded-full animate-pulse" style="top: 107px; left: 107px;"></div>
            </div>
        </div>

        <!-- 底部操作栏 -->
        <div class="bg-ios-card border-t border-ios-bg p-3 space-y-3">
            <!-- 观众信息 -->
            <div class="flex justify-between items-center text-sm">
                <div class="flex items-center space-x-2">
                    <span class="text-ios-gray">观众:</span>
                    <div class="flex -space-x-1">
                        <div class="w-6 h-6 bg-ios-blue rounded-full border-2 border-white flex items-center justify-center">
                            <span class="text-white text-xs">A</span>
                        </div>
                        <div class="w-6 h-6 bg-ios-green rounded-full border-2 border-white flex items-center justify-center">
                            <span class="text-white text-xs">B</span>
                        </div>
                        <div class="w-6 h-6 bg-ios-orange rounded-full border-2 border-white flex items-center justify-center">
                            <span class="text-white text-xs">C</span>
                        </div>
                    </div>
                    <span class="text-ios-gray">3人</span>
                </div>
                <button class="text-ios-blue text-sm" onclick="toggleMobileHistory()">
                    查看记录
                </button>
            </div>
            
            <!-- 操作按钮 -->
            <div class="grid grid-cols-4 gap-2">
                <button class="bg-ios-gray text-white py-2 rounded-lg text-sm btn-cartoon">
                    悔棋
                </button>
                <button class="bg-ios-orange text-white py-2 rounded-lg text-sm btn-cartoon">
                    和棋
                </button>
                <button class="bg-ios-red text-white py-2 rounded-lg text-sm btn-cartoon">
                    认输
                </button>
                <button class="bg-ios-green text-white py-2 rounded-lg text-sm btn-cartoon">
                    再来
                </button>
            </div>
        </div>

        <!-- 移动端游戏记录 (滑出面板) -->
        <div id="mobileHistory" class="fixed inset-x-0 bottom-0 bg-ios-card rounded-t-3xl shadow-2xl transform translate-y-full transition-transform duration-300 z-50">
            <div class="p-4">
                <div class="w-12 h-1 bg-ios-gray rounded-full mx-auto mb-4"></div>
                <h3 class="font-semibold text-lg mb-3">游戏记录</h3>
                <div class="max-h-64 overflow-y-auto space-y-2">
                    <div class="flex justify-between items-center py-2 border-b border-ios-bg">
                        <span class="text-ios-gray">第1步</span>
                        <span>黑棋 (7,7)</span>
                    </div>
                    <div class="flex justify-between items-center py-2 border-b border-ios-bg">
                        <span class="text-ios-gray">第2步</span>
                        <span>白棋 (8,8)</span>
                    </div>
                    <div class="flex justify-between items-center py-2 border-b border-ios-bg">
                        <span class="text-ios-gray">第3步</span>
                        <span>黑棋 (6,6)</span>
                    </div>
                    <div class="flex justify-between items-center py-2 border-b border-ios-bg">
                        <span class="text-ios-gray">第4步</span>
                        <span>白棋 (9,9)</span>
                    </div>
                    <div class="flex justify-between items-center py-2 border-b border-ios-bg">
                        <span class="text-ios-gray">第5步</span>
                        <span>黑棋 (5,5)</span>
                    </div>
                    <div class="flex justify-between items-center py-2 bg-ios-bg rounded">
                        <span class="text-ios-gray">第6步</span>
                        <span class="font-semibold">黑棋 (4,4)</span>
                    </div>
                </div>
                <button onclick="toggleMobileHistory()" class="w-full bg-ios-blue text-white py-3 rounded-lg mt-4 btn-cartoon">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <script>
        function toggleMobileHistory() {
            const panel = document.getElementById('mobileHistory');
            panel.classList.toggle('translate-y-full');
        }

        // 移动端棋盘交互
        document.addEventListener('DOMContentLoaded', function() {
            const board = document.getElementById('mobileBoard');
            
            board.addEventListener('touchstart', function(e) {
                e.preventDefault();
                const touch = e.touches[0];
                const rect = board.getBoundingClientRect();
                const x = touch.clientX - rect.left;
                const y = touch.clientY - rect.top;
                
                const cellSize = 18;
                const col = Math.round((x - 12) / cellSize);
                const row = Math.round((y - 12) / cellSize);
                
                if (row >= 0 && row < 15 && col >= 0 && col < 15) {
                    // 触觉反馈
                    if (navigator.vibrate) {
                        navigator.vibrate(50);
                    }
                    
                    // 添加棋子
                    const piece = document.createElement('div');
                    piece.className = 'absolute w-5 h-5 piece-black rounded-full';
                    piece.style.left = (col * cellSize + 12 - 10) + 'px';
                    piece.style.top = (row * cellSize + 12 - 10) + 'px';
                    board.appendChild(piece);
                    
                    console.log(`移动端落子: (${row}, ${col})`);
                }
            });
        });
    </script>
</body>
</html>
