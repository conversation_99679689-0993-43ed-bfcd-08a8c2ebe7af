/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // iOS 风格配色
        'ios': {
          'blue': '#007AFF',
          'blue-light': '#5AC8FA',
          'blue-dark': '#0051D5',
          'green': '#34C759',
          'green-light': '#30D158',
          'orange': '#FF9500',
          'red': '#FF3B30',
          'bg': '#F2F2F7',
          'card': '#FFFFFF',
          'tertiary': '#E5E5EA',
          'gray': '#8E8E93',
          'gray-light': '#C7C7CC',
          'text': '#1C1C1E',
        },
        // 游戏专用色彩
        'game': {
          'board-bg': '#DEB887',
          'board-line': '#8B4513',
          'black-piece': '#2C2C2E',
          'white-piece': '#F2F2F7',
        }
      },
      borderRadius: {
        'ios': '12px',
        'cartoon': '20px',
      },
      boxShadow: {
        'ios': '0 4px 20px rgba(0, 0, 0, 0.08)',
        'cartoon': '0 8px 24px rgba(0, 0, 0, 0.12)',
        'piece': '0 2px 4px rgba(0, 0, 0, 0.3)',
      },
      animation: {
        'drop-in': 'dropIn 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        'pulse-slow': 'pulse 2s infinite',
        'fade-in': 'fadeIn 0.5s ease-in',
      },
      keyframes: {
        dropIn: {
          '0%': {
            transform: 'scale(0) translateY(-20px)',
            opacity: '0',
          },
          '100%': {
            transform: 'scale(1) translateY(0)',
            opacity: '1',
          },
        },
        fadeIn: {
          '0%': {
            opacity: '0',
            transform: 'translateY(20px)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%)',
        'gradient-success': 'linear-gradient(135deg, #34C759 0%, #30D158 100%)',
        'gradient-bg': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'piece-black': 'radial-gradient(circle at 30% 30%, #4A4A4A, #1C1C1E)',
        'piece-white': 'radial-gradient(circle at 30% 30%, #FFFFFF, #F2F2F7)',
      },
      fontFamily: {
        'mono': ['SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'monospace'],
      },
    },
  },
  plugins: [require("daisyui")],
  daisyui: {
    themes: ["light", "dark", "cupcake", "synthwave", "retro", "cyberpunk"],
  },
} 