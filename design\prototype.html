<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>五子棋游戏 - UI原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'ios-blue': '#007AFF',
                        'ios-blue-light': '#5AC8FA',
                        'ios-green': '#34C759',
                        'ios-orange': '#FF9500',
                        'ios-red': '#FF3B30',
                        'ios-bg': '#F2F2F7',
                        'ios-card': '#FFFFFF',
                        'ios-gray': '#8E8E93',
                        'board-bg': '#DEB887',
                        'board-line': '#8B4513'
                    },
                    borderRadius: {
                        'ios': '12px',
                        'cartoon': '20px'
                    },
                    boxShadow: {
                        'ios': '0 4px 20px rgba(0, 0, 0, 0.08)',
                        'cartoon': '0 8px 24px rgba(0, 0, 0, 0.12)'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .gradient-primary {
            background: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
        }
        .piece-black {
            background: radial-gradient(circle at 30% 30%, #4A4A4A, #1C1C1E);
            border: 2px solid #000;
        }
        .piece-white {
            background: radial-gradient(circle at 30% 30%, #FFFFFF, #F2F2F7);
            border: 2px solid #D1D1D6;
        }
        .board-grid {
            background-image:
                linear-gradient(to right, #8B4513 1px, transparent 1px),
                linear-gradient(to bottom, #8B4513 1px, transparent 1px);
            background-size: 30px 30px;
            background-position: 0 0, 0 0;
        }
        .board-grid::before {
            content: '';
            position: absolute;
            inset: 0;
            background-image:
                linear-gradient(to right, #8B4513 2px, transparent 2px),
                linear-gradient(to bottom, #8B4513 2px, transparent 2px);
            background-size: 30px 30px;
            background-position: 0 0, 0 0;
            opacity: 0.3;
        }
        .hover-lift:hover {
            transform: translateY(-2px);
            transition: transform 0.3s ease;
        }
        .btn-cartoon {
            transition: transform 0.2s ease;
        }
        .btn-cartoon:hover {
            transform: scale(1.05);
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-ios-bg min-h-screen">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-4xl mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <h1 class="text-xl font-bold text-gray-800">五子棋对战</h1>
                <div class="flex space-x-2">
                    <button class="p-2 rounded-full hover:bg-gray-100">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 py-8">
        <!-- 首页原型 -->
        <div id="homepage" class="space-y-8 fade-in">
            <!-- 游戏标题 -->
            <div class="text-center">
                <div class="text-6xl mb-4">🎮</div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">五子棋对战</h1>
                <p class="text-ios-gray">与朋友一起享受经典五子棋游戏</p>
            </div>

            <!-- 主要功能卡片 -->
            <div class="grid md:grid-cols-2 gap-6">
                <!-- 创建房间 -->
                <div class="bg-ios-card rounded-cartoon p-6 shadow-cartoon hover-lift">
                    <div class="text-center">
                        <div class="w-16 h-16 gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">创建房间</h3>
                        <p class="text-ios-gray mb-4">创建新的游戏房间，邀请朋友加入</p>
                        <button class="gradient-primary text-white px-6 py-3 rounded-ios font-semibold btn-cartoon w-full">
                            创建新游戏
                        </button>
                    </div>
                </div>

                <!-- 加入房间 -->
                <div class="bg-ios-card rounded-cartoon p-6 shadow-cartoon hover-lift">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-ios-green rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-4">加入房间</h3>
                        <div class="space-y-3">
                            <input type="text" placeholder="输入房间号" class="w-full px-4 py-3 border border-gray-200 rounded-ios focus:border-ios-blue focus:ring-2 focus:ring-ios-blue focus:ring-opacity-20 outline-none">
                            <input type="password" placeholder="房间密码（可选）" class="w-full px-4 py-3 border border-gray-200 rounded-ios focus:border-ios-blue focus:ring-2 focus:ring-ios-blue focus:ring-opacity-20 outline-none">
                            <button class="bg-ios-green text-white px-6 py-3 rounded-ios font-semibold btn-cartoon w-full">
                                加入游戏
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 游戏说明 -->
            <div class="bg-ios-card rounded-cartoon p-6 shadow-ios">
                <h3 class="text-xl font-semibold mb-4 flex items-center">
                    <span class="text-2xl mr-2">📖</span>
                    游戏说明
                </h3>
                <div class="grid md:grid-cols-3 gap-4 text-sm">
                    <div class="flex items-start space-x-3">
                        <span class="text-ios-blue text-lg">🎯</span>
                        <div>
                            <h4 class="font-semibold">五子连珠</h4>
                            <p class="text-ios-gray">率先连成五子即可获胜</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <span class="text-ios-green text-lg">👥</span>
                        <div>
                            <h4 class="font-semibold">观战模式</h4>
                            <p class="text-ios-gray">支持多人观看游戏进展</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <span class="text-ios-orange text-lg">🔗</span>
                        <div>
                            <h4 class="font-semibold">分享链接</h4>
                            <p class="text-ios-gray">一键分享房间给朋友</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 游戏房间原型 -->
        <div id="gameroom" class="space-y-6 fade-in hidden">
            <!-- 房间信息栏 -->
            <div class="bg-ios-card rounded-ios p-4 shadow-ios flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-ios-gray">房间号:</span>
                    <span class="font-mono text-lg font-bold">123456</span>
                </div>
                <div class="flex space-x-2">
                    <button class="bg-ios-blue text-white px-4 py-2 rounded-ios text-sm btn-cartoon">
                        分享
                    </button>
                    <button class="bg-ios-red text-white px-4 py-2 rounded-ios text-sm btn-cartoon">
                        退出
                    </button>
                </div>
            </div>

            <!-- 玩家信息 -->
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-ios-card rounded-ios p-4 shadow-ios text-center">
                    <div class="w-12 h-12 piece-black rounded-full mx-auto mb-2"></div>
                    <h3 class="font-semibold">玩家1</h3>
                    <span class="text-xs bg-ios-green text-white px-2 py-1 rounded-full">在线</span>
                </div>
                <div class="bg-ios-card rounded-ios p-4 shadow-ios text-center">
                    <div class="w-12 h-12 piece-white rounded-full mx-auto mb-2"></div>
                    <h3 class="font-semibold">玩家2</h3>
                    <span class="text-xs bg-ios-green text-white px-2 py-1 rounded-full">在线</span>
                </div>
            </div>

            <!-- 棋盘区域 -->
            <div class="bg-ios-card rounded-cartoon p-6 shadow-cartoon">
                <div class="bg-board-bg rounded-ios p-4 board-grid aspect-square max-w-lg mx-auto relative cursor-pointer" id="gameBoard">
                    <!-- 棋盘交叉点标记 -->
                    <div class="absolute w-2 h-2 bg-board-line rounded-full" style="top: 114px; left: 114px;"></div>
                    <div class="absolute w-2 h-2 bg-board-line rounded-full" style="top: 114px; left: 234px;"></div>
                    <div class="absolute w-2 h-2 bg-board-line rounded-full" style="top: 114px; left: 354px;"></div>
                    <div class="absolute w-2 h-2 bg-board-line rounded-full" style="top: 234px; left: 114px;"></div>
                    <div class="absolute w-2 h-2 bg-board-line rounded-full" style="top: 234px; left: 234px;"></div>
                    <div class="absolute w-2 h-2 bg-board-line rounded-full" style="top: 234px; left: 354px;"></div>
                    <div class="absolute w-2 h-2 bg-board-line rounded-full" style="top: 354px; left: 114px;"></div>
                    <div class="absolute w-2 h-2 bg-board-line rounded-full" style="top: 354px; left: 234px;"></div>
                    <div class="absolute w-2 h-2 bg-board-line rounded-full" style="top: 354px; left: 354px;"></div>

                    <!-- 示例棋子 - 展示一个进行中的游戏 -->
                    <div class="absolute w-7 h-7 piece-black rounded-full animate-drop-in" style="top: 220px; left: 220px;"></div>
                    <div class="absolute w-7 h-7 piece-white rounded-full animate-drop-in" style="top: 250px; left: 250px;"></div>
                    <div class="absolute w-7 h-7 piece-black rounded-full animate-drop-in" style="top: 190px; left: 190px;"></div>
                    <div class="absolute w-7 h-7 piece-white rounded-full animate-drop-in" style="top: 280px; left: 280px;"></div>
                    <div class="absolute w-7 h-7 piece-black rounded-full animate-drop-in" style="top: 160px; left: 160px;"></div>
                    <div class="absolute w-7 h-7 piece-white rounded-full animate-drop-in" style="top: 310px; left: 310px;"></div>
                    <div class="absolute w-7 h-7 piece-black rounded-full animate-drop-in" style="top: 130px; left: 130px;"></div>

                    <!-- 最后一步标记 -->
                    <div class="absolute w-3 h-3 bg-ios-red rounded-full animate-pulse" style="top: 125px; left: 125px;"></div>

                    <!-- 悬停效果示例 -->
                    <div class="absolute w-7 h-7 border-2 border-ios-blue border-dashed rounded-full opacity-50 hidden" id="hoverPiece" style="top: 100px; left: 100px;"></div>
                </div>

                <!-- 棋盘操作提示 -->
                <div class="mt-4 text-center text-sm text-ios-gray">
                    <span class="inline-flex items-center space-x-2">
                        <div class="w-4 h-4 piece-black rounded-full"></div>
                        <span>轮到黑棋下子</span>
                    </span>
                </div>
            </div>

            <!-- 游戏状态栏 -->
            <div class="bg-ios-card rounded-ios p-4 shadow-ios">
                <div class="flex justify-between items-center mb-3">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-ios-gray">当前轮次:</span>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 piece-black rounded-full animate-pulse-slow"></div>
                            <span class="font-semibold">黑棋</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-ios-gray">剩余时间:</span>
                        <div class="bg-ios-orange text-white px-3 py-1 rounded-full">
                            <span class="font-mono text-sm font-bold">00:30</span>
                        </div>
                    </div>
                </div>

                <!-- 游戏进度 -->
                <div class="flex justify-between items-center text-sm text-ios-gray mb-3">
                    <span>第 7 步</span>
                    <span>游戏进行中</span>
                </div>

                <!-- 操作按钮 -->
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-ios-gray">观众: 3人</span>
                        <button class="text-ios-blue text-sm hover:underline" onclick="toggleObservers()">
                            查看观众
                        </button>
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-ios-gray text-white px-3 py-2 rounded-ios text-sm btn-cartoon">
                            悔棋
                        </button>
                        <button class="bg-ios-green text-white px-3 py-2 rounded-ios text-sm btn-cartoon">
                            认输
                        </button>
                        <button class="bg-ios-blue text-white px-3 py-2 rounded-ios text-sm btn-cartoon">
                            和棋
                        </button>
                    </div>
                </div>
            </div>

            <!-- 观众列表 (可折叠) -->
            <div id="observersList" class="bg-ios-card rounded-ios p-4 shadow-ios hidden">
                <h4 class="font-semibold mb-3 flex items-center">
                    <span class="text-lg mr-2">👥</span>
                    观众列表 (3人)
                </h4>
                <div class="grid grid-cols-3 gap-3">
                    <div class="text-center">
                        <div class="w-8 h-8 bg-ios-blue rounded-full flex items-center justify-center mx-auto mb-1">
                            <span class="text-white text-xs font-bold">A</span>
                        </div>
                        <span class="text-xs text-ios-gray">观众A</span>
                    </div>
                    <div class="text-center">
                        <div class="w-8 h-8 bg-ios-green rounded-full flex items-center justify-center mx-auto mb-1">
                            <span class="text-white text-xs font-bold">B</span>
                        </div>
                        <span class="text-xs text-ios-gray">观众B</span>
                    </div>
                    <div class="text-center">
                        <div class="w-8 h-8 bg-ios-orange rounded-full flex items-center justify-center mx-auto mb-1">
                            <span class="text-white text-xs font-bold">C</span>
                        </div>
                        <span class="text-xs text-ios-gray">观众C</span>
                    </div>
                </div>
            </div>

            <!-- 游戏历史/步骤记录 -->
            <div class="bg-ios-card rounded-ios p-4 shadow-ios">
                <h4 class="font-semibold mb-3 flex items-center">
                    <span class="text-lg mr-2">📝</span>
                    游戏记录
                </h4>
                <div class="max-h-32 overflow-y-auto space-y-1 text-sm">
                    <div class="flex justify-between items-center py-1 border-b border-ios-tertiary">
                        <span class="text-ios-gray">第1步</span>
                        <span>黑棋 (7,7)</span>
                    </div>
                    <div class="flex justify-between items-center py-1 border-b border-ios-tertiary">
                        <span class="text-ios-gray">第2步</span>
                        <span>白棋 (8,8)</span>
                    </div>
                    <div class="flex justify-between items-center py-1 border-b border-ios-tertiary">
                        <span class="text-ios-gray">第3步</span>
                        <span>黑棋 (6,6)</span>
                    </div>
                    <div class="flex justify-between items-center py-1 border-b border-ios-tertiary">
                        <span class="text-ios-gray">第4步</span>
                        <span>白棋 (9,9)</span>
                    </div>
                    <div class="flex justify-between items-center py-1 border-b border-ios-tertiary">
                        <span class="text-ios-gray">第5步</span>
                        <span>黑棋 (5,5)</span>
                    </div>
                    <div class="flex justify-between items-center py-1 border-b border-ios-tertiary">
                        <span class="text-ios-gray">第6步</span>
                        <span>白棋 (10,10)</span>
                    </div>
                    <div class="flex justify-between items-center py-1 bg-ios-bg rounded">
                        <span class="text-ios-gray">第7步</span>
                        <span class="font-semibold">黑棋 (4,4)</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 游戏结束模态框 -->
        <div id="gameEndModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-ios-card rounded-cartoon p-8 mx-4 max-w-sm w-full text-center shadow-cartoon">
                <div class="text-6xl mb-4">🎉</div>
                <h2 class="text-2xl font-bold text-ios-text mb-2">游戏结束</h2>
                <div class="flex items-center justify-center space-x-2 mb-4">
                    <div class="w-8 h-8 piece-black rounded-full"></div>
                    <span class="text-lg font-semibold">黑棋获胜！</span>
                </div>
                <p class="text-ios-gray mb-6">恭喜获得胜利！</p>
                <div class="flex space-x-3">
                    <button class="flex-1 bg-ios-blue text-white py-3 rounded-ios font-semibold btn-cartoon" onclick="hideGameEndModal()">
                        再来一局
                    </button>
                    <button class="flex-1 bg-ios-gray text-white py-3 rounded-ios font-semibold btn-cartoon" onclick="hideGameEndModal()">
                        返回首页
                    </button>
                </div>
            </div>
        </div>

        <!-- 切换按钮 -->
        <div class="fixed bottom-6 right-6 flex flex-col space-y-2">
            <button onclick="showHomepage()" class="bg-ios-blue text-white p-3 rounded-full shadow-lg btn-cartoon" title="首页">
                🏠
            </button>
            <button onclick="showGameroom()" class="bg-ios-green text-white p-3 rounded-full shadow-lg btn-cartoon" title="游戏房间">
                🎮
            </button>
            <button onclick="showGameEndModal()" class="bg-ios-orange text-white p-3 rounded-full shadow-lg btn-cartoon" title="游戏结束">
                🏆
            </button>
        </div>
    </div>

    <script>
        function showHomepage() {
            document.getElementById('homepage').classList.remove('hidden');
            document.getElementById('gameroom').classList.add('hidden');
            document.getElementById('gameEndModal').classList.add('hidden');
        }

        function showGameroom() {
            document.getElementById('homepage').classList.add('hidden');
            document.getElementById('gameroom').classList.remove('hidden');
            document.getElementById('gameEndModal').classList.add('hidden');
        }

        function showGameEndModal() {
            document.getElementById('gameEndModal').classList.remove('hidden');
        }

        function hideGameEndModal() {
            document.getElementById('gameEndModal').classList.add('hidden');
        }

        function toggleObservers() {
            const observersList = document.getElementById('observersList');
            observersList.classList.toggle('hidden');
        }

        // 棋盘交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const gameBoard = document.getElementById('gameBoard');
            const hoverPiece = document.getElementById('hoverPiece');

            if (gameBoard && hoverPiece) {
                gameBoard.addEventListener('mousemove', function(e) {
                    const rect = gameBoard.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    // 计算最近的交叉点
                    const cellSize = 30;
                    const col = Math.round((x - 16) / cellSize);
                    const row = Math.round((y - 16) / cellSize);

                    if (row >= 0 && row < 15 && col >= 0 && col < 15) {
                        hoverPiece.style.left = (col * cellSize + 16 - 14) + 'px';
                        hoverPiece.style.top = (row * cellSize + 16 - 14) + 'px';
                        hoverPiece.classList.remove('hidden');
                    } else {
                        hoverPiece.classList.add('hidden');
                    }
                });

                gameBoard.addEventListener('mouseleave', function() {
                    hoverPiece.classList.add('hidden');
                });

                gameBoard.addEventListener('click', function(e) {
                    const rect = gameBoard.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    const cellSize = 30;
                    const col = Math.round((x - 16) / cellSize);
                    const row = Math.round((y - 16) / cellSize);

                    if (row >= 0 && row < 15 && col >= 0 && col < 15) {
                        // 模拟落子效果
                        const piece = document.createElement('div');
                        piece.className = 'absolute w-7 h-7 piece-black rounded-full animate-drop-in';
                        piece.style.left = (col * cellSize + 16 - 14) + 'px';
                        piece.style.top = (row * cellSize + 16 - 14) + 'px';
                        gameBoard.appendChild(piece);

                        // 播放落子音效 (模拟)
                        console.log(`落子位置: (${row}, ${col})`);
                    }
                });
            }
        });

        // 模拟倒计时
        let timeLeft = 30;
        function updateTimer() {
            const timerElements = document.querySelectorAll('.font-mono');
            timerElements.forEach(el => {
                if (el.textContent.includes(':')) {
                    const minutes = Math.floor(timeLeft / 60);
                    const seconds = timeLeft % 60;
                    el.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }
            });

            if (timeLeft > 0) {
                timeLeft--;
                setTimeout(updateTimer, 1000);
            } else {
                // 时间到，显示游戏结束
                showGameEndModal();
            }
        }

        // 启动倒计时
        setTimeout(updateTimer, 1000);
    </script>
</body>
</html>
