# 五子棋游戏 UI 设计文档

## 设计理念

### 整体风格
- **卡通风格**：友好、可爱、轻松的视觉体验
- **iOS设计语言**：简洁、现代、直观的交互设计
- **跨平台适配**：移动端优先，PC端兼容

### 设计原则
1. **简洁明了**：界面元素清晰，信息层级分明
2. **友好可爱**：卡通化的视觉元素，降低游戏门槛
3. **一致性**：统一的设计语言和交互模式
4. **可访问性**：适合各年龄段用户使用

## 配色方案

### 主色调 (iOS 风格)
```css
/* 主色 - iOS 蓝色 */
--primary-color: #007AFF;
--primary-light: #5AC8FA;
--primary-dark: #0051D5;

/* 辅助色 */
--success-color: #34C759;  /* iOS 绿色 */
--warning-color: #FF9500;  /* iOS 橙色 */
--error-color: #FF3B30;    /* iOS 红色 */

/* 背景色 */
--bg-primary: #F2F2F7;     /* 浅灰背景 */
--bg-secondary: #FFFFFF;   /* 白色背景 */
--bg-tertiary: #E5E5EA;    /* 中性灰 */

/* 文字色 */
--text-primary: #1C1C1E;   /* 深灰文字 */
--text-secondary: #8E8E93; /* 中灰文字 */
--text-tertiary: #C7C7CC;  /* 浅灰文字 */

/* 棋盘和棋子 */
--board-bg: #DEB887;       /* 木质棋盘色 */
--board-line: #8B4513;     /* 棋盘线条 */
--black-piece: #2C2C2E;    /* 黑棋 */
--white-piece: #F2F2F7;    /* 白棋 */
```

### 渐变色
```css
/* 背景渐变 */
--gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

/* 按钮渐变 */
--gradient-primary: linear-gradient(135deg, #007AFF 0%, #5AC8FA 100%);
--gradient-success: linear-gradient(135deg, #34C759 0%, #30D158 100%);
```

## 组件设计

### 1. 按钮组件
```css
/* 主要按钮 */
.btn-primary {
  background: var(--gradient-primary);
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

/* 次要按钮 */
.btn-secondary {
  background: var(--bg-secondary);
  border: 2px solid var(--primary-color);
  border-radius: 12px;
  padding: 10px 22px;
  color: var(--primary-color);
}

/* 卡通风格圆角 */
.btn-cartoon {
  border-radius: 20px;
  transform: scale(1);
  transition: transform 0.2s ease;
}

.btn-cartoon:hover {
  transform: scale(1.05);
}
```

### 2. 输入框组件
```css
.input-field {
  background: var(--bg-secondary);
  border: 2px solid var(--bg-tertiary);
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.input-field:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
}
```

### 3. 卡片组件
```css
.card {
  background: var(--bg-secondary);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--bg-tertiary);
}

.card-cartoon {
  border-radius: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}
```

## 页面布局设计

### 1. 首页布局
```
┌─────────────────────────────────┐
│           游戏标题              │
│        🎮 五子棋对战            │
├─────────────────────────────────┤
│                                 │
│    ┌─────────────────────┐      │
│    │    创建房间         │      │
│    │  [创建新游戏]       │      │
│    └─────────────────────┘      │
│                                 │
│    ┌─────────────────────┐      │
│    │    加入房间         │      │
│    │  房间号: [______]   │      │
│    │  密码:   [______]   │      │
│    │      [加入游戏]     │      │
│    └─────────────────────┘      │
│                                 │
│    ┌─────────────────────┐      │
│    │    游戏说明         │      │
│    │  • 五子连珠获胜     │      │
│    │  • 支持观战模式     │      │
│    │  • 可分享房间链接   │      │
│    └─────────────────────┘      │
└─────────────────────────────────┘
```

### 2. 游戏房间布局 (PC端)
```
┌─────────────────────────────────┐
│  房间: 123456  [分享] [退出]    │
├─────────────────────────────────┤
│  玩家1        VS        玩家2   │
│  ●黑棋                  ○白棋   │
│  [在线]                 [在线]  │
├─────────────────────────────────┤
│                                 │
│        ┌─────────────┐          │
│        │   15x15     │          │
│        │   棋盘区域   │          │
│        │   ●○●○      │          │
│        │   ○●○●      │          │
│        └─────────────┘          │
│                                 │
├─────────────────────────────────┤
│  当前轮次: ●黑棋  ⏰ 00:30      │
│  观众: 👥3人  [查看观众]        │
├─────────────────────────────────┤
│  [悔棋] [和棋] [认输] [再来一局] │
├─────────────────────────────────┤
│  📝 游戏记录                    │
│  第1步: 黑棋 (7,7)              │
│  第2步: 白棋 (8,8)              │
│  第3步: 黑棋 (6,6)              │
│  ...                            │
└─────────────────────────────────┘
```

### 3. 移动端游戏房间布局
```
┌─────────────────┐
│ 房间:123456 [分享]│
├─────────────────┤
│ ●玩家1  VS  玩家2○│
│ [在线]     [在线] │
├─────────────────┤
│ ●黑棋回合 ⏰00:25 │
├─────────────────┤
│                 │
│   ┌───────────┐ │
│   │ 15x15棋盘 │ │
│   │   ●○●○   │ │
│   │   ○●○●   │ │
│   └───────────┘ │
│                 │
├─────────────────┤
│ 观众:👥ABC 3人   │
│ [悔棋][和棋][认输]│
│ [查看记录]       │
└─────────────────┘
```

## 棋盘和棋子设计

### 棋盘设计
- **尺寸**: 15x15 标准五子棋棋盘
- **背景**: 木质纹理，温暖的棕色调
- **线条**: 深棕色，1px 实线
- **交叉点**: 小圆点标记重要位置
- **响应式**: 根据屏幕大小自动调整

### 棋子设计
```css
/* 黑棋 */
.piece-black {
  background: radial-gradient(circle at 30% 30%, #4A4A4A, #1C1C1E);
  border: 2px solid #000;
  border-radius: 50%;
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

/* 白棋 */
.piece-white {
  background: radial-gradient(circle at 30% 30%, #FFFFFF, #F2F2F7);
  border: 2px solid #D1D1D6;
  border-radius: 50%;
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.2),
    inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 落子动画 */
.piece-drop {
  animation: dropIn 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes dropIn {
  0% {
    transform: scale(0) translateY(-20px);
    opacity: 0;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}
```

## 响应式设计

### 断点设置
```css
/* 移动端 */
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }
  
  .board {
    max-width: 90vw;
    max-height: 90vw;
  }
  
  .piece {
    width: 20px;
    height: 20px;
  }
}

/* 平板端 */
@media (min-width: 769px) and (max-width: 1024px) {
  .container {
    padding: 24px;
  }
  
  .board {
    max-width: 500px;
    max-height: 500px;
  }
  
  .piece {
    width: 28px;
    height: 28px;
  }
}

/* 桌面端 */
@media (min-width: 1025px) {
  .container {
    padding: 32px;
  }
  
  .board {
    max-width: 600px;
    max-height: 600px;
  }
  
  .piece {
    width: 32px;
    height: 32px;
  }
}
```

## 动画效果

### 页面过渡
```css
.page-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}
```

### 交互反馈
```css
/* 悬停效果 */
.interactive:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 点击效果 */
.interactive:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

/* 脉冲动画 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 122, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 122, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 122, 255, 0);
  }
}
```

## 图标设计

### 使用 Heroicons 图标库
- **房间**: home-modern
- **分享**: share
- **设置**: cog-6-tooth
- **用户**: user-circle
- **退出**: arrow-right-on-rectangle
- **复制**: clipboard-document
- **刷新**: arrow-path

### 自定义图标
- **黑棋**: ● (Unicode: U+25CF)
- **白棋**: ○ (Unicode: U+25CB)
- **五子棋**: 🎯 (Unicode: U+1F3AF)

## 加载和状态设计

### 加载动画
```css
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--bg-tertiary);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

### 状态提示
```css
.status-online {
  color: var(--success-color);
}

.status-offline {
  color: var(--text-tertiary);
}

.status-waiting {
  color: var(--warning-color);
}
```

这个UI设计文档为整个五子棋游戏提供了完整的视觉指导，包括配色方案、组件设计、布局结构和动画效果。
