{"name": "cloudbase-vue-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@cloudbase/js-sdk": "^2.16.0", "@heroicons/vue": "^2.2.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.10.4", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "autoprefixer": "^10.4.21", "daisyui": "^5.0.35", "eslint": "^9.15.0", "eslint-plugin-vue": "^9.31.0", "postcss": "^8.5.3", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}