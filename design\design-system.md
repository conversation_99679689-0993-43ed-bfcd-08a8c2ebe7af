# 五子棋游戏设计系统

## 设计概览

本设计系统为五子棋游戏提供了完整的视觉规范，采用卡通风格和iOS配色方案，确保在移动端和PC端都有优秀的用户体验。

## 核心设计原则

### 1. 卡通友好
- 圆润的边角设计
- 柔和的阴影效果
- 生动的动画过渡
- 温暖的色彩搭配

### 2. iOS设计语言
- 简洁明了的界面
- 一致的交互模式
- 清晰的信息层级
- 优雅的视觉反馈

### 3. 响应式优先
- 移动端优先设计
- 灵活的布局系统
- 自适应的组件尺寸
- 触摸友好的交互

## 色彩系统

### 主色调
```css
--ios-blue: #007AFF        /* 主要操作按钮 */
--ios-blue-light: #5AC8FA  /* 悬停状态 */
--ios-blue-dark: #0051D5   /* 按下状态 */
```

### 功能色彩
```css
--ios-green: #34C759       /* 成功/在线状态 */
--ios-orange: #FF9500      /* 警告/等待状态 */
--ios-red: #FF3B30         /* 错误/危险操作 */
```

### 中性色彩
```css
--ios-bg: #F2F2F7          /* 页面背景 */
--ios-card: #FFFFFF        /* 卡片背景 */
--ios-tertiary: #E5E5EA    /* 边框/分割线 */
--ios-gray: #8E8E93        /* 次要文字 */
--ios-text: #1C1C1E        /* 主要文字 */
```

### 游戏专用色彩
```css
--game-board-bg: #DEB887   /* 棋盘背景 */
--game-board-line: #8B4513 /* 棋盘线条 */
--game-black-piece: #2C2C2E /* 黑棋 */
--game-white-piece: #F2F2F7 /* 白棋 */
```

## 字体系统

### 字体族
- **系统字体**: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto
- **等宽字体**: 'SF Mono', Monaco, 'Roboto Mono', monospace

### 字体大小
```css
text-xs: 12px     /* 标签、状态 */
text-sm: 14px     /* 辅助信息 */
text-base: 16px   /* 正文 */
text-lg: 18px     /* 小标题 */
text-xl: 20px     /* 标题 */
text-2xl: 24px    /* 大标题 */
text-3xl: 30px    /* 页面标题 */
```

### 字重
```css
font-normal: 400  /* 正文 */
font-medium: 500  /* 强调 */
font-semibold: 600 /* 按钮、标题 */
font-bold: 700    /* 重要标题 */
```

## 间距系统

### 基础间距
```css
1: 4px    /* 极小间距 */
2: 8px    /* 小间距 */
3: 12px   /* 中小间距 */
4: 16px   /* 标准间距 */
6: 24px   /* 中等间距 */
8: 32px   /* 大间距 */
12: 48px  /* 超大间距 */
```

### 组件内边距
```css
padding-sm: 16px   /* 小组件 */
padding-md: 24px   /* 标准组件 */
padding-lg: 32px   /* 大组件 */
```

## 圆角系统

### 圆角规格
```css
rounded-ios: 12px      /* iOS标准圆角 */
rounded-cartoon: 20px  /* 卡通风格圆角 */
rounded-full: 50%      /* 圆形元素 */
```

### 使用场景
- **按钮**: rounded-ios 或 rounded-cartoon
- **卡片**: rounded-ios 或 rounded-cartoon
- **输入框**: rounded-ios
- **棋子**: rounded-full

## 阴影系统

### 阴影层级
```css
shadow-ios: 0 4px 20px rgba(0, 0, 0, 0.08)      /* 标准卡片 */
shadow-cartoon: 0 8px 24px rgba(0, 0, 0, 0.12)  /* 卡通风格 */
shadow-piece: 0 2px 4px rgba(0, 0, 0, 0.3)      /* 棋子阴影 */
```

### 使用原则
- 卡片使用 shadow-ios
- 卡通风格组件使用 shadow-cartoon
- 游戏棋子使用 shadow-piece
- 悬停状态增强阴影效果

## 动画系统

### 过渡时间
```css
duration-150: 150ms  /* 快速反馈 */
duration-300: 300ms  /* 标准过渡 */
duration-500: 500ms  /* 慢速过渡 */
```

### 缓动函数
```css
ease-in: cubic-bezier(0.4, 0, 1, 1)
ease-out: cubic-bezier(0, 0, 0.2, 1)
ease-in-out: cubic-bezier(0.4, 0, 0.2, 1)
bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55)
```

### 动画效果
```css
/* 落子动画 */
@keyframes dropIn {
  0% { transform: scale(0) translateY(-20px); opacity: 0; }
  100% { transform: scale(1) translateY(0); opacity: 1; }
}

/* 淡入动画 */
@keyframes fadeIn {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

/* 脉冲动画 */
@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(0, 122, 255, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(0, 122, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(0, 122, 255, 0); }
}
```

## 图标系统

### 图标库
使用 Heroicons 作为主要图标库，确保图标风格一致。

### 常用图标
```javascript
// 功能图标
home: 'home-modern'           // 首页
share: 'share'                // 分享
settings: 'cog-6-tooth'       // 设置
user: 'user-circle'           // 用户
exit: 'arrow-right-on-rectangle' // 退出
copy: 'clipboard-document'    // 复制
refresh: 'arrow-path'         // 刷新

// 游戏图标
target: '🎯'                  // 五子棋
black-piece: '●'             // 黑棋
white-piece: '○'             // 白棋
```

### 图标尺寸
```css
w-4 h-4: 16px    /* 小图标 */
w-5 h-5: 20px    /* 标准图标 */
w-6 h-6: 24px    /* 大图标 */
w-8 h-8: 32px    /* 超大图标 */
```

## 响应式断点

### 断点定义
```css
sm: 640px    /* 小屏手机 */
md: 768px    /* 大屏手机/小平板 */
lg: 1024px   /* 平板 */
xl: 1280px   /* 桌面 */
```

### 布局策略
- **移动端优先**: 默认样式针对移动端
- **渐进增强**: 大屏幕添加更多功能
- **触摸友好**: 按钮最小44px点击区域

## 组件状态

### 交互状态
```css
/* 默认状态 */
.interactive {
  transition: all 0.3s ease;
}

/* 悬停状态 */
.interactive:hover {
  transform: translateY(-2px);
  box-shadow: enhanced;
}

/* 激活状态 */
.interactive:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

/* 禁用状态 */
.interactive:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
```

### 焦点状态
```css
.focusable:focus {
  outline: none;
  ring: 2px solid ios-blue;
  ring-opacity: 0.5;
}
```

## 可访问性

### 颜色对比度
- 主要文字: 4.5:1 对比度
- 大文字: 3:1 对比度
- 非文字元素: 3:1 对比度

### 键盘导航
- 所有交互元素支持Tab导航
- 明确的焦点指示器
- 合理的Tab顺序

### 屏幕阅读器
- 语义化HTML结构
- 适当的ARIA标签
- 有意义的alt文本

## 设计文件

### 原型文件
- `design/prototype.html` - 交互式原型
- `design/ui-design.md` - 详细设计规范
- `design/components.md` - 组件设计文档

### 配置文件
- `tailwind.config.js` - Tailwind CSS配置
- `src/style.css` - 全局样式

### 设计资源
- 配色方案已集成到Tailwind配置
- 组件样式使用Tailwind类名
- 自定义CSS用于特殊效果

## 设计检查清单

### 视觉一致性
- [ ] 使用统一的配色方案
- [ ] 保持一致的圆角规格
- [ ] 统一的阴影效果
- [ ] 一致的字体使用

### 交互体验
- [ ] 明确的交互反馈
- [ ] 流畅的动画过渡
- [ ] 合理的加载状态
- [ ] 友好的错误提示

### 响应式设计
- [ ] 移动端适配
- [ ] 平板端优化
- [ ] 桌面端增强
- [ ] 触摸操作友好

### 可访问性
- [ ] 颜色对比度达标
- [ ] 键盘导航支持
- [ ] 屏幕阅读器友好
- [ ] 语义化HTML

这个设计系统为整个五子棋游戏项目提供了完整的视觉指导和实现规范。
