# 五子棋游戏组件设计规范

## 基础组件

### 1. 按钮组件 (BaseButton.vue)

#### 主要按钮
```vue
<template>
  <button 
    :class="buttonClasses"
    @click="$emit('click')"
    :disabled="disabled"
  >
    <slot />
  </button>
</template>

<script setup>
const props = defineProps({
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'success', 'warning', 'danger'].includes(value)
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  disabled: Boolean,
  cartoon: Boolean
})

const buttonClasses = computed(() => {
  const base = 'font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-opacity-50'
  
  const variants = {
    primary: 'bg-gradient-primary text-white hover:shadow-lg focus:ring-ios-blue',
    secondary: 'bg-ios-card border-2 border-ios-blue text-ios-blue hover:bg-ios-blue hover:text-white',
    success: 'bg-gradient-success text-white hover:shadow-lg focus:ring-ios-green',
    warning: 'bg-ios-orange text-white hover:shadow-lg focus:ring-ios-orange',
    danger: 'bg-ios-red text-white hover:shadow-lg focus:ring-ios-red'
  }
  
  const sizes = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-4 text-lg'
  }
  
  const radius = props.cartoon ? 'rounded-cartoon' : 'rounded-ios'
  const hover = props.cartoon ? 'hover:scale-105' : 'hover:-translate-y-0.5'
  const disabled = props.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
  
  return [base, variants[props.variant], sizes[props.size], radius, hover, disabled].join(' ')
})
</script>
```

### 2. 输入框组件 (BaseInput.vue)

```vue
<template>
  <div class="relative">
    <input
      :type="type"
      :placeholder="placeholder"
      :value="modelValue"
      @input="$emit('update:modelValue', $event.target.value)"
      :class="inputClasses"
      :disabled="disabled"
    />
    <div v-if="error" class="mt-1 text-sm text-ios-red">
      {{ error }}
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  modelValue: String,
  type: {
    type: String,
    default: 'text'
  },
  placeholder: String,
  error: String,
  disabled: Boolean
})

const inputClasses = computed(() => {
  const base = 'w-full px-4 py-3 border rounded-ios transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-opacity-20'
  const normal = 'border-ios-tertiary focus:border-ios-blue focus:ring-ios-blue'
  const errorState = 'border-ios-red focus:border-ios-red focus:ring-ios-red'
  const disabled = props.disabled ? 'bg-ios-bg cursor-not-allowed' : 'bg-ios-card'
  
  return [base, props.error ? errorState : normal, disabled].join(' ')
})
</script>
```

### 3. 卡片组件 (BaseCard.vue)

```vue
<template>
  <div :class="cardClasses">
    <slot />
  </div>
</template>

<script setup>
const props = defineProps({
  cartoon: Boolean,
  hover: Boolean,
  padding: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  }
})

const cardClasses = computed(() => {
  const base = 'bg-ios-card border border-ios-tertiary'
  const radius = props.cartoon ? 'rounded-cartoon' : 'rounded-ios'
  const shadow = props.cartoon ? 'shadow-cartoon' : 'shadow-ios'
  const hover = props.hover ? 'hover:-translate-y-1 hover:shadow-lg transition-all duration-300' : ''
  
  const paddings = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  }
  
  return [base, radius, shadow, hover, paddings[props.padding]].join(' ')
})
</script>
```

## 游戏专用组件

### 4. 棋盘组件 (GameBoard.vue)

```vue
<template>
  <div class="game-board-container">
    <div 
      ref="boardRef"
      class="game-board bg-game-board-bg rounded-ios p-4 relative select-none"
      :style="boardStyle"
      @click="handleBoardClick"
    >
      <!-- 棋盘网格 -->
      <div class="board-grid absolute inset-4" :style="gridStyle"></div>
      
      <!-- 棋子 -->
      <div
        v-for="(piece, index) in pieces"
        :key="index"
        :class="pieceClasses(piece)"
        :style="pieceStyle(piece)"
        class="absolute rounded-full shadow-piece transition-all duration-300"
      >
      </div>
      
      <!-- 最后一步标记 -->
      <div
        v-if="lastMove"
        class="absolute w-2 h-2 bg-ios-red rounded-full"
        :style="lastMoveStyle"
      ></div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  board: {
    type: Array,
    required: true
  },
  disabled: Boolean,
  lastMove: Object
})

const emit = defineEmits(['move'])

const boardRef = ref(null)
const boardSize = ref(400)
const cellSize = computed(() => boardSize.value / 15)

const boardStyle = computed(() => ({
  width: `${boardSize.value}px`,
  height: `${boardSize.value}px`,
  maxWidth: '90vw',
  maxHeight: '90vw'
}))

const gridStyle = computed(() => ({
  backgroundImage: `
    linear-gradient(to right, #8B4513 1px, transparent 1px),
    linear-gradient(to bottom, #8B4513 1px, transparent 1px)
  `,
  backgroundSize: `${cellSize.value}px ${cellSize.value}px`
}))

const pieces = computed(() => {
  const result = []
  for (let row = 0; row < 15; row++) {
    for (let col = 0; col < 15; col++) {
      if (props.board[row][col] !== 0) {
        result.push({
          row,
          col,
          color: props.board[row][col] === 1 ? 'black' : 'white'
        })
      }
    }
  }
  return result
})

const pieceClasses = (piece) => {
  return piece.color === 'black' 
    ? 'bg-piece-black border-2 border-black' 
    : 'bg-piece-white border-2 border-ios-tertiary'
}

const pieceStyle = (piece) => {
  const size = cellSize.value * 0.8
  const offset = (cellSize.value - size) / 2
  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${piece.col * cellSize.value + offset + 16}px`,
    top: `${piece.row * cellSize.value + offset + 16}px`
  }
}

const lastMoveStyle = computed(() => {
  if (!props.lastMove) return {}
  return {
    left: `${props.lastMove.col * cellSize.value + cellSize.value / 2 + 16 - 4}px`,
    top: `${props.lastMove.row * cellSize.value + cellSize.value / 2 + 16 - 4}px`
  }
})

const handleBoardClick = (event) => {
  if (props.disabled) return
  
  const rect = boardRef.value.getBoundingClientRect()
  const x = event.clientX - rect.left - 16
  const y = event.clientY - rect.top - 16
  
  const col = Math.round(x / cellSize.value)
  const row = Math.round(y / cellSize.value)
  
  if (row >= 0 && row < 15 && col >= 0 && col < 15) {
    emit('move', { row, col })
  }
}

// 响应式调整棋盘大小
onMounted(() => {
  const updateSize = () => {
    const container = boardRef.value?.parentElement
    if (container) {
      const maxSize = Math.min(container.clientWidth - 32, container.clientHeight - 32, 600)
      boardSize.value = Math.max(maxSize, 300)
    }
  }
  
  updateSize()
  window.addEventListener('resize', updateSize)
  
  onUnmounted(() => {
    window.removeEventListener('resize', updateSize)
  })
})
</script>
```

### 5. 棋子组件 (GamePiece.vue)

```vue
<template>
  <div 
    :class="pieceClasses"
    class="game-piece rounded-full shadow-piece animate-drop-in"
    :style="pieceStyle"
  >
  </div>
</template>

<script setup>
const props = defineProps({
  color: {
    type: String,
    required: true,
    validator: (value) => ['black', 'white'].includes(value)
  },
  size: {
    type: Number,
    default: 32
  }
})

const pieceClasses = computed(() => {
  return props.color === 'black' 
    ? 'bg-piece-black border-2 border-black' 
    : 'bg-piece-white border-2 border-ios-tertiary'
})

const pieceStyle = computed(() => ({
  width: `${props.size}px`,
  height: `${props.size}px`
}))
</script>
```

### 6. 玩家信息组件 (PlayerInfo.vue)

```vue
<template>
  <BaseCard :cartoon="true" :hover="false" padding="md">
    <div class="text-center">
      <!-- 玩家头像/棋子 -->
      <div class="relative mx-auto mb-3" :style="{ width: '48px', height: '48px' }">
        <GamePiece :color="playerColor" :size="48" />
        <div 
          v-if="isCurrentPlayer"
          class="absolute -inset-1 rounded-full border-2 border-ios-blue animate-pulse-slow"
        ></div>
      </div>
      
      <!-- 玩家名称 -->
      <h3 class="font-semibold text-ios-text mb-2">{{ playerName }}</h3>
      
      <!-- 在线状态 -->
      <div class="flex items-center justify-center space-x-2">
        <div 
          :class="statusClasses"
          class="w-2 h-2 rounded-full"
        ></div>
        <span :class="statusTextClasses" class="text-xs font-medium px-2 py-1 rounded-full">
          {{ statusText }}
        </span>
      </div>
      
      <!-- 游戏统计 -->
      <div v-if="showStats" class="mt-3 text-xs text-ios-gray">
        <div>胜: {{ wins }} 负: {{ losses }}</div>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
const props = defineProps({
  playerName: {
    type: String,
    required: true
  },
  playerColor: {
    type: String,
    required: true,
    validator: (value) => ['black', 'white'].includes(value)
  },
  isOnline: {
    type: Boolean,
    default: true
  },
  isCurrentPlayer: {
    type: Boolean,
    default: false
  },
  wins: {
    type: Number,
    default: 0
  },
  losses: {
    type: Number,
    default: 0
  },
  showStats: {
    type: Boolean,
    default: false
  }
})

const statusClasses = computed(() => {
  return props.isOnline ? 'bg-ios-green' : 'bg-ios-gray'
})

const statusTextClasses = computed(() => {
  return props.isOnline 
    ? 'bg-ios-green text-white' 
    : 'bg-ios-tertiary text-ios-gray'
})

const statusText = computed(() => {
  return props.isOnline ? '在线' : '离线'
})
</script>
```

## 布局组件

### 7. 游戏布局组件 (GameLayout.vue)

```vue
<template>
  <div class="min-h-screen bg-ios-bg">
    <!-- 导航栏 -->
    <nav class="bg-ios-card shadow-sm border-b border-ios-tertiary">
      <div class="max-w-4xl mx-auto px-4 py-3">
        <div class="flex justify-between items-center">
          <h1 class="text-xl font-bold text-ios-text">{{ title }}</h1>
          <div class="flex space-x-2">
            <slot name="nav-actions" />
          </div>
        </div>
      </div>
    </nav>
    
    <!-- 主要内容 -->
    <main class="max-w-4xl mx-auto px-4 py-8">
      <slot />
    </main>
    
    <!-- 浮动操作按钮 -->
    <div v-if="$slots.fab" class="fixed bottom-6 right-6">
      <slot name="fab" />
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    default: '五子棋对战'
  }
})
</script>
```

## 使用示例

### 在页面中使用组件

```vue
<template>
  <GameLayout title="游戏房间">
    <template #nav-actions>
      <BaseButton variant="primary" size="sm" @click="shareRoom">
        分享
      </BaseButton>
      <BaseButton variant="danger" size="sm" @click="leaveRoom">
        退出
      </BaseButton>
    </template>
    
    <div class="space-y-6">
      <!-- 玩家信息 -->
      <div class="grid grid-cols-2 gap-4">
        <PlayerInfo
          player-name="玩家1"
          player-color="black"
          :is-online="true"
          :is-current-player="currentPlayer === 'black'"
        />
        <PlayerInfo
          player-name="玩家2"
          player-color="white"
          :is-online="true"
          :is-current-player="currentPlayer === 'white'"
        />
      </div>
      
      <!-- 棋盘 -->
      <BaseCard cartoon>
        <GameBoard
          :board="gameBoard"
          :disabled="!isMyTurn"
          :last-move="lastMove"
          @move="makeMove"
        />
      </BaseCard>
    </div>
  </GameLayout>
</template>
```

这个组件设计规范提供了完整的UI组件库，确保整个游戏界面的一致性和可维护性。
